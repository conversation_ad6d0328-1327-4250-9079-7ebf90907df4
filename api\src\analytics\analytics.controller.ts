import {
	Controller,
	Get,
	Post,
	Query,
	Body,
	Param,
	UseGuards,
	Res,
	ValidationPipe,
	Req
} from '@nestjs/common';
import {
	ApiBearerAuth,
	ApiOperation,
	ApiTags,
	ApiResponse
} from '@nestjs/swagger';
import { Response } from 'express';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../roles/roles.decorator';
import { Role } from '../roles/role.enum';
import { AnalyticsService } from './analytics.service';
import {
	DownloadAnalyticsReportDto,
	GetRevenueChartDataDto,
	RevenueChartDataPoint,
	CollectedPaymentsChartDataPoint,
	GetAppointmentsChartDataDto,
	AppointmentsChartResponse,
	DoctorSummaryResponseDto,
	GetDoctorSummaryDto,
	SummaryResponseDto,
	GetSummaryDto
} from './dto/analytics.dto';
import {
	ShareAnalyticsDocumentsDto,
	ShareAnalyticsDocumentsResponseDto,
	AnalyticsDocumentStatusDto
} from '../analytics-sharing/dto/share-analytics-documents.dto';
import { AnalyticsDocumentService } from '../analytics-sharing/services/analytics-document.service';
import { AnalyticsMonitoringService } from '../analytics-sharing/services/analytics-monitoring.service';
import {
	AnalyticsDocumentStatus
} from '../analytics-sharing/entities/analytics-document-request.entity';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';
import { RequestWithUser } from '../auth/interfaces/request-with-user.interface';

@ApiTags('Analytics')
@Controller('analytics')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class AnalyticsController {
	constructor(
		private readonly analyticsService: AnalyticsService,
		private readonly analyticsDocumentService: AnalyticsDocumentService,
		private readonly analyticsMonitoringService: AnalyticsMonitoringService
	) {}

	@Get('revenue-chart-data')
	@Roles(Role.ADMIN)
	@ApiOperation({ summary: 'Get revenue chart data' })
	@TrackMethod('get-revenue-chart-data')
	async getRevenueChartData(
		@Query(new ValidationPipe({ transform: true }))
		dto: GetRevenueChartDataDto
	): Promise<RevenueChartDataPoint[]> {
		return await this.analyticsService.getRevenueChartData(dto);
	}

	@Get('collected-payments-chart-data')
	@Roles(Role.ADMIN)
	@ApiOperation({ summary: 'Get collected payments chart data' })
	@TrackMethod('get-collected-payments-chart-data')
	async getCollectedPaymentsChartData(
		@Query(new ValidationPipe({ transform: true }))
		dto: GetRevenueChartDataDto
	): Promise<CollectedPaymentsChartDataPoint[]> {
		return await this.analyticsService.getCollectedPaymentsChartData(dto);
	}

	@Get('appointments-chart-data')
	@Roles(Role.ADMIN)
	@ApiOperation({ summary: 'Get appointments chart data' })
	@TrackMethod('get-appointments-chart-data')
	async getAppointmentsChartData(
		@Query(new ValidationPipe({ transform: true }))
		dto: GetAppointmentsChartDataDto
	): Promise<AppointmentsChartResponse> {
		return await this.analyticsService.getAppointmentsChartData(dto);
	}

	@Get('download-report')
	@Roles(Role.ADMIN)
	@ApiOperation({ summary: 'Download analytics report' })
	@TrackMethod('download-analytics-report')
	async downloadReport(
		@Query(new ValidationPipe({ transform: true }))
		dto: DownloadAnalyticsReportDto,
		@Res() res: Response
	): Promise<void> {
		const buffer = await this.analyticsService.generateReport(dto);

		res.set({
			'Content-Type':
				'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			'Content-Disposition': `attachment; filename="${dto.type}-report.xlsx"`,
			'Content-Length': buffer.length,
			'Cache-Control': 'no-cache'
		});

		res.end(buffer);
	}

	@Get('doctor-summary')
	@Roles(Role.ADMIN)
	@ApiOperation({ summary: 'Get doctor performance summary' })
	@TrackMethod('get-doctor-summary')
	async getDoctorSummary(
		@Query(new ValidationPipe({ transform: true }))
		dto: GetDoctorSummaryDto
	): Promise<DoctorSummaryResponseDto[]> {
		return await this.analyticsService.getDoctorSummary(dto);
	}

	@Get('summary')
	@Roles(Role.ADMIN)
	@ApiOperation({ summary: 'Get clinic performance summary' })
	@ApiResponse({
		status: 200,
		description: 'Returns summary data for clinic'
	})
	@TrackMethod('get-clinic-summary')
	async getSummary(
		@Query(new ValidationPipe({ transform: true })) dto: GetSummaryDto
	): Promise<SummaryResponseDto> {
		return await this.analyticsService.getSummary(dto);
	}

	@Post('share-documents')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.RECEPTIONIST)
	@ApiOperation({
		summary: 'Share analytics documents via email',
		description: 'Generate and share analytics documents (PDF + Excel) for a specific document type and date range'
	})
	@ApiResponse({
		status: 201,
		description: 'Analytics document request created successfully',
		type: ShareAnalyticsDocumentsResponseDto
	})
	@ApiResponse({
		status: 400,
		description: 'Invalid request parameters'
	})
	@TrackMethod('share-analytics-documents')
	async shareAnalyticsDocuments(
		@Body(new ValidationPipe({ transform: true })) dto: ShareAnalyticsDocumentsDto,
		@Req() req: RequestWithUser
	): Promise<ShareAnalyticsDocumentsResponseDto> {
		// Get user ID from JWT context
		const userId = req.user.userId || req.user.id;
		if (!userId) {
			throw new Error('User ID not found in JWT context');
		}

		// Pass recipient email as-is, let the service handle CLIENT type resolution

		// Create analytics document request and get the database-generated ID
		const requestId = await this.analyticsDocumentService.shareAnalyticsDocuments({
			clinicId: dto.clinicId,
			brandId: dto.brandId, // Use the brandId from the request
			userId,
			documentType: dto.documentType,
			recipientType: dto.recipientType,
			recipientEmail: dto.recipientEmail,
			recipientPhone: dto.recipientPhone,
			startDate: new Date(dto.startDate),
			endDate: new Date(dto.endDate)
		});

		return {
			requestId,
			status: AnalyticsDocumentStatus.PENDING,
			message: 'Analytics document request has been queued for processing. You will receive an email when the documents are ready.'
		};
	}

	@Get('share-documents/:requestId/status')
	@Roles(Role.ADMIN, Role.DOCTOR, Role.RECEPTIONIST)
	@ApiOperation({
		summary: 'Get analytics document request status',
		description: 'Check the status of an analytics document sharing request'
	})
	@ApiResponse({
		status: 200,
		description: 'Analytics document request status',
		type: AnalyticsDocumentStatusDto
	})
	@ApiResponse({
		status: 404,
		description: 'Analytics document request not found'
	})
	@TrackMethod('get-analytics-document-status')
	async getAnalyticsDocumentStatus(
		@Param('requestId') requestId: string
	): Promise<AnalyticsDocumentStatusDto> {
		return await this.analyticsDocumentService.getAnalyticsDocumentStatus(requestId);
	}

	@Get('metrics')
	@Roles(Role.ADMIN, Role.SUPER_ADMIN)
	@ApiOperation({ summary: 'Get analytics processing metrics' })
	@ApiResponse({ status: 200, description: 'Analytics metrics retrieved successfully' })
	@TrackMethod('get-analytics-metrics')
	async getAnalyticsMetrics() {
		return await this.analyticsMonitoringService.getAnalyticsMetrics();
	}

	@Get('health')
	@Roles(Role.ADMIN, Role.SUPER_ADMIN)
	@ApiOperation({ summary: 'Get analytics system health metrics' })
	@ApiResponse({ status: 200, description: 'System health metrics retrieved successfully' })
	@TrackMethod('get-analytics-health')
	async getSystemHealth() {
		return await this.analyticsMonitoringService.getSystemHealthMetrics();
	}

	@Get('cleanup-metrics')
	@Roles(Role.SUPER_ADMIN)
	@ApiOperation({ summary: 'Get analytics document cleanup metrics' })
	@ApiResponse({ status: 200, description: 'Cleanup metrics retrieved successfully' })
	@TrackMethod('get-analytics-cleanup-metrics')
	async getCleanupMetrics() {
		return await this.analyticsDocumentService.getCleanupMetrics();
	}
}
