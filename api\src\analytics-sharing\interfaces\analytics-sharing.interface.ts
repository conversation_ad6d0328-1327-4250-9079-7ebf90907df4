import {
	AnalyticsDocumentType,
	AnalyticsRecipientType
} from '../entities/analytics-document-request.entity';

export interface AnalyticsDocumentRequest {
	requestId?: string; // Optional since it will be generated by the database
	clinicId: string;
	brandId: string;
	userId: string;
	documentType: AnalyticsDocumentType;
	recipientType: AnalyticsRecipientType;
	recipientEmail?: string;
	recipientPhone?: string;
	startDate: Date;
	endDate: Date;
}

export interface AnalyticsDocumentProcessingResult {
	pdfBuffer: Buffer;
	excelBuffer: Buffer;
	documentCount: number;
	totalSize: number;
	pdfFileKey?: string;
	excelFileKey?: string;
}

export interface ExcelReportData {
	invoices?: InvoiceExcelRow[];
	receipts?: ReceiptExcelRow[];
	creditNotes?: CreditNoteExcelRow[];
}

export interface InvoiceExcelRow {
	date: string;
	client: string;
	pet: string;
	invoiceNumber: string;
	invoiceStatus: string;
	invoiceAmount: number;
	invoiceBalance: number;
}

export interface ReceiptExcelRow {
	date: string;
	client: string;
	receiptNumber: string;
	amount: number;
	transaction: string;
	paymentMode: string;
}

export interface CreditNoteExcelRow {
	date: string;
	client: string;
	creditNoteNumber: string;
	referenceInvoice: string;
	amountReturned: number;
}

export interface AnalyticsDocumentFilters {
	clinicId: string;
	brandId: string;
	startDate: Date;
	endDate: Date;
	documentType: AnalyticsDocumentType;
}

export interface BatchProcessingConfig {
	batchSize: number;
	maxDocuments: number;
	maxPeriodDays: number;
}

export const DEFAULT_BATCH_CONFIG: BatchProcessingConfig = {
	batchSize: 100,
	maxDocuments: 5000,
	maxPeriodDays: 31
};
