"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const config_1 = require("@nestjs/config");
const dotenv_1 = require("dotenv");
const fs = require("fs");
(0, dotenv_1.config)({ path: '.env' });
exports.default = (0, config_1.registerAs)('database', () => {
    const sslEnabled = process.env.NODE_ENV === 'development' ? false : true; // If true, use certificates
    const dbSslCAPath = process.env.NODE_ENV === 'qa' ? 'certificates/isrgrootx1.pem' : 'certificates/ap-south-1-bundle.pem';
    // SSL config for valid certificates (for QA, UAT, and Production)
    const sslConfig = sslEnabled
        ? {
            ssl: {
                rejectUnauthorized: !sslEnabled, // Reject unauthorized for all except local (self-signed)
                ca: dbSslCAPath && fs.existsSync(dbSslCAPath)
                    ? fs.readFileSync(dbSslCAPath)
                    : undefined,
            },
        }
        : {};
    return {
        type: 'postgres',
        host: process.env.DB_HOST || '',
        port: parseInt(process.env.DB_PORT || '', 10),
        username: process.env.DB_USERNAME || '',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_DATABASE_NAME || '',
        entities: [__dirname + '/../**/*.entity{.ts,.js}'],
        migrations: [__dirname + '/../migrations/*{.ts,.js}'],
        synchronize: false,
        logging: process.env.NODE_ENV !== 'prod',
        migrationsTableName: 'migrations_typeorm',
        extra: {
            max: 30,
            min: 2,
            idleTimeoutMillis: 30000,
            connectionTimeoutMillis: 2000,
            ...sslConfig, // Apply the SSL configuration if enabled
        },
    };
});
//# sourceMappingURL=database.config.js.map