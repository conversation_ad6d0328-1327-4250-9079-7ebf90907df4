"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SESMailService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const winston_logger_service_1 = require("../../logger/winston-logger.service");
const AWS = require("aws-sdk");
const nodemailer_1 = require("nodemailer");
let SESMailService = class SESMailService {
    constructor(configService, loggerService) {
        this.configService = configService;
        this.loggerService = loggerService;
        this.sesClient = new AWS.SES({
            region: 'ap-south-1',
            accessKeyId: process.env.AWS_SES_ACCESS_KEY_ID,
            secretAccessKey: process.env.AWS_SES_SECRET_ACCESS_KEY
        });
    }
    async sendMail({ body, subject, toMailAddress, ccMailAddress, pdfBuffers, pdfFileNames }) {
        return new Promise((resolve, rejects) => {
            try {
                const attachments = pdfBuffers === null || pdfBuffers === void 0 ? void 0 : pdfBuffers.map((buffer, idx) => ({
                    filename: (pdfFileNames === null || pdfFileNames === void 0 ? void 0 : pdfFileNames[idx]) || `Attachment_${idx}.pdf`,
                    content: buffer
                }));
                const transporter = (0, nodemailer_1.createTransport)({
                    SES: { ses: this.sesClient, aws: AWS }
                });
                // Use verified email for UAT environment
                const sourceEmail = process.env.NODE_ENV === 'uat'
                    ? '<EMAIL>'
                    : process.env.SES_SOURCE_EMAIL;
                const mailOptions = {
                    from: sourceEmail,
                    subject,
                    html: body,
                    to: toMailAddress,
                    cc: ccMailAddress,
                    attachments
                };
                this.loggerService.log('mail options details', {
                    from: process.env.SES_SOURCE_EMAIL,
                    subject,
                    html: body,
                    to: toMailAddress,
                    cc: ccMailAddress,
                });
                transporter.sendMail(mailOptions, (err, response) => {
                    if (err) {
                        this.loggerService.error('Error sending email:', err.message);
                        //return rejects(err); // Log and reject the error.
                    }
                    this.loggerService.log('Email Response');
                    resolve(response);
                });
            }
            catch (err) {
                const error = {
                    err,
                    message: 'something went wrong during sending mail '
                };
                this.loggerService.log('Email Error', {
                    err
                });
                return rejects(error);
            }
        });
    }
};
exports.SESMailService = SESMailService;
exports.SESMailService = SESMailService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        winston_logger_service_1.WinstonLogger])
], SESMailService);
//# sourceMappingURL=send-mail-service.js.map