declare const _default: (() => {
    s3: {
        bucket: string;
        accessKeyId: string;
        secretKey: string;
        region: string;
    };
    cloudwatch: {
        logGroupName: string;
        logStreamName: string;
        accessKeyId: string;
        secretKey: string;
        region: string;
    };
    cognito: {
        userPoolId: string;
        clientId: string;
        region: string;
    };
    sqs: {
        queueUrl: string;
        accessKeyId: string;
        secretKey: string;
        region: string;
    };
}) & import("@nestjs/config").ConfigFactoryKeyHost<{
    s3: {
        bucket: string;
        accessKeyId: string;
        secretKey: string;
        region: string;
    };
    cloudwatch: {
        logGroupName: string;
        logStreamName: string;
        accessKeyId: string;
        secretKey: string;
        region: string;
    };
    cognito: {
        userPoolId: string;
        clientId: string;
        region: string;
    };
    sqs: {
        queueUrl: string;
        accessKeyId: string;
        secretKey: string;
        region: string;
    };
}>;
export default _default;
