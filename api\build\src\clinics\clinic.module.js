"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClinicModule = void 0;
const common_1 = require("@nestjs/common");
const clinic_service_1 = require("./clinic.service");
const clinic_controller_1 = require("./clinic.controller");
const clinic_entity_1 = require("./entities/clinic.entity");
const typeorm_1 = require("@nestjs/typeorm");
const clinic_room_entity_1 = require("./entities/clinic-room.entity");
const role_module_1 = require("../roles/role.module");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const clinic_consumables_module_1 = require("../clinic-consumables/clinic-consumables.module");
const clinic_user_entity_1 = require("./entities/clinic-user.entity");
const clinic_medications_module_1 = require("../clinic-medications/clinic-medications.module");
const clinic_lab_report_module_1 = require("../clinic-lab-report/clinic-lab-report.module");
const clinic_products_module_1 = require("../clinic-products/clinic-products.module");
const clinic_services_module_1 = require("../clinic-services/clinic-services.module");
const clinic_vaccinations_module_1 = require("../clinic-vaccinations/clinic-vaccinations.module");
const users_module_1 = require("../users/users.module");
const send_mail_service_1 = require("../utils/aws/ses/send-mail-service");
const brands_module_1 = require("../brands/brands.module");
let ClinicModule = class ClinicModule {
};
exports.ClinicModule = ClinicModule;
exports.ClinicModule = ClinicModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([clinic_entity_1.ClinicEntity, clinic_room_entity_1.ClinicRoomEntity, clinic_user_entity_1.ClinicUser]),
            role_module_1.RoleModule,
            clinic_consumables_module_1.ClinicConsumablesModule,
            clinic_medications_module_1.ClinicMedicationsModule,
            clinic_lab_report_module_1.ClinicLabReportModule,
            clinic_products_module_1.ClinicProductsModule,
            clinic_services_module_1.ClinicServicesModule,
            clinic_vaccinations_module_1.ClinicVaccinationsModule,
            (0, common_1.forwardRef)(() => users_module_1.UsersModule),
            (0, common_1.forwardRef)(() => brands_module_1.BrandsModule)
        ],
        controllers: [clinic_controller_1.ClinicController],
        providers: [clinic_service_1.ClinicService, winston_logger_service_1.WinstonLogger, send_mail_service_1.SESMailService],
        exports: [clinic_service_1.ClinicService]
    })
], ClinicModule);
//# sourceMappingURL=clinic.module.js.map