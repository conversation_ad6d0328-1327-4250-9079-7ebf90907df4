declare const _default: (() => {
    type: string;
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
    entities: string[];
    migrations: string[];
    synchronize: boolean;
    logging: boolean;
    migrationsTableName: string;
    extra: {
        ssl: {
            rejectUnauthorized: boolean;
            ca: Buffer | undefined;
        };
        max: number;
        min: number;
        idleTimeoutMillis: number;
        connectionTimeoutMillis: number;
    } | {
        ssl?: undefined;
        max: number;
        min: number;
        idleTimeoutMillis: number;
        connectionTimeoutMillis: number;
    };
}) & import("@nestjs/config").ConfigFactoryKeyHost<{
    type: string;
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
    entities: string[];
    migrations: string[];
    synchronize: boolean;
    logging: boolean;
    migrationsTableName: string;
    extra: {
        ssl: {
            rejectUnauthorized: boolean;
            ca: Buffer | undefined;
        };
        max: number;
        min: number;
        idleTimeoutMillis: number;
        connectionTimeoutMillis: number;
    } | {
        ssl?: undefined;
        max: number;
        min: number;
        idleTimeoutMillis: number;
        connectionTimeoutMillis: number;
    };
}>;
export default _default;
