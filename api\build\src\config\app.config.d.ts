declare const _default: (() => {
    apiPort: number;
    sqsPort: number;
    nodeEnv: string;
    jwtSecret: string | undefined;
    emailVerificationURL: string | undefined;
    global: {
        limit: number;
        duration: number;
    };
    otp: {
        limit: number;
        duration: number;
    };
    blacklistedIPs: string[];
    allowedRegions: string[];
    webSocketPort: number;
}) & import("@nestjs/config").ConfigFactoryKeyHost<{
    apiPort: number;
    sqsPort: number;
    nodeEnv: string;
    jwtSecret: string | undefined;
    emailVerificationURL: string | undefined;
    global: {
        limit: number;
        duration: number;
    };
    otp: {
        limit: number;
        duration: number;
    };
    blacklistedIPs: string[];
    allowedRegions: string[];
    webSocketPort: number;
}>;
export default _default;
