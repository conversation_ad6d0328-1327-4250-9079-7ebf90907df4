import React from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { Modal } from '@/app/molecules';
import { Button, Text } from '@/app/atoms';
import RadioButtonGroup, {
    RadioButtonItemsT,
} from '@/app/molecules/RadiobuttonGroup';
import RenderFields from '@/app/molecules/RenderFields';
import RangeDatePicker from '@/app/molecules/RangeDatePicker';

interface ShareAnalyticsModalProps {
    isOpen: boolean;
    onClose: () => void;
    handleCancel: () => void;
    handleShare: (data: AnalyticsFormValues) => void;
    title: string;
    isLoading?: boolean;
}

// Analytics-specific form values
export type AnalyticsFormValues = {
    documentType: 'INVOICE' | 'RECEIPT' | 'CREDIT_NOTE';
    recipient: 'client' | 'other';
    email?: string;
    startDate: Date | null;
    endDate: Date | null;
};

// Validation schema for analytics sharing
const AnalyticsSchema = yup.object().shape({
    documentType: yup
        .string()
        .oneOf(['INVOICE', 'RECEIPT', 'CREDIT_NOTE'])
        .required('Document type is required'),
    recipient: yup
        .string()
        .oneOf(['client', 'other'])
        .required('Recipient is required'),
    email: yup.string().when('recipient', {
        is: 'other',
        then: (schema) =>
            schema
                .email('Please enter a valid email address')
                .required('Email is required for other recipients'),
        otherwise: (schema) => schema.notRequired(),
    }),
    startDate: yup.date().nullable().required('Start date is required'),
    endDate: yup
        .date()
        .nullable()
        .required('End date is required')
        .test(
            'is-after-start',
            'End date must be after start date',
            function (value) {
                const { startDate } = this.parent;
                if (!startDate || !value) return true;
                return value >= startDate;
            }
        ),
});

const ShareAnalyticsModal: React.FC<ShareAnalyticsModalProps> = ({
    isOpen,
    onClose,
    handleCancel,
    handleShare,
    title,
    isLoading = false,
}) => {
    const documentTypeRadioItems: RadioButtonItemsT[] = [
        {
            id: 'INVOICE',
            label: 'Invoice',
            value: 'INVOICE',
            defaultChecked: true,
        },
        {
            id: 'RECEIPT',
            label: 'Receipt',
            value: 'RECEIPT',
        },
        {
            id: 'CREDIT_NOTE',
            label: 'Credit Note',
            value: 'CREDIT_NOTE',
        },
    ];

    const recipientRadioItems: RadioButtonItemsT[] = [
        {
            id: 'client',
            label: 'Myself',
            value: 'client',
            defaultChecked: true,
        },
        { id: 'other', label: 'Other', value: 'other' },
    ];

    const {
        control,
        formState: { errors, isValid },
        handleSubmit,
        setValue,
        watch,
        trigger,
        reset,
    } = useForm<AnalyticsFormValues>({
        resolver: yupResolver(AnalyticsSchema) as any,
        defaultValues: {
            documentType: 'INVOICE',
            recipient: 'client',
            email: '',
            startDate: null,
            endDate: null,
        },
        mode: 'all',
    });

    const onSubmit = (data: AnalyticsFormValues) => {
        handleShare(data);
    };

    const recipient = watch('recipient');

    // Reset form when modal opens
    React.useEffect(() => {
        if (isOpen) {
            reset({
                documentType: 'INVOICE',
                recipient: 'client',
                email: '',
                startDate: null,
                endDate: null,
            });
        }
    }, [isOpen, reset]);

    const renderModalFooter = () => {
        return (
            <div className="flex justify-end gap-x-4">
                <Button
                    id="cancel-share"
                    variant="secondary"
                    type="button"
                    onClick={handleCancel}
                    disabled={isLoading}
                >
                    Cancel
                </Button>
                <Button
                    id="share-analytics"
                    variant="primary"
                    type="button"
                    onClick={handleSubmit(onSubmit)}
                    disabled={!isValid || isLoading}
                >
                    {isLoading ? 'Sharing...' : 'Share Documents'}
                </Button>
            </div>
        );
    };

    return (
        <Modal
            isOpen={isOpen}
            isHeaderBorder={true}
            isModalSubTitle={true}
            modalTitle={title}
            onClose={onClose}
            dataAutomation="share-analytics"
            modalFooter={renderModalFooter()}
        >
            <div className="flex flex-col">
                <form
                    id="analytics-share-form"
                    onSubmit={handleSubmit(onSubmit)}
                    className="gap-5 flex flex-col mb-2"
                >
                    {/* Document Type Selection */}
                    <div className="flex flex-col gap-2">
                        <Text variant="bodySmall" fontWeight="font-semibold">
                            Select Document Type
                        </Text>
                        <Controller
                            name="documentType"
                            control={control}
                            render={({ field }) => (
                                <RadioButtonGroup
                                    id="selectDocumentType"
                                    direction="column"
                                    radioButtonItems={documentTypeRadioItems}
                                    isRequired={true}
                                    errorMessage={errors.documentType?.message}
                                    size="medium"
                                    {...field}
                                    onChange={(value) => {
                                        // Extract the string value from the object
                                        const stringValue =
                                            typeof value === 'object' &&
                                            value?.value
                                                ? value.value
                                                : value;
                                        field.onChange(stringValue);
                                    }}
                                />
                            )}
                        />
                        {errors.documentType && (
                            <Text variant="caption" textColor="text-red-500">
                                {errors.documentType.message}
                            </Text>
                        )}
                    </div>

                    {/* Date Range Selection */}
                    <div className="flex flex-col gap-2">
                        <Text variant="bodySmall" fontWeight="font-semibold">
                            Select Date Range
                        </Text>
                        <Controller
                            name="startDate"
                            control={control}
                            render={({ field }) => (
                                <RangeDatePicker
                                    id="dateRange"
                                    name="dateRange"
                                    dateFormat="dd/MM/yyyy"
                                    placeholderStart="Start Date"
                                    placeholderEnd="End Date"
                                    required={true}
                                    errorMessage={
                                        errors.startDate?.message ||
                                        errors.endDate?.message
                                    }
                                    onDateChange={(dates) => {
                                        setValue('startDate', dates.startDate);
                                        setValue('endDate', dates.endDate);
                                        trigger(['startDate', 'endDate']);
                                    }}
                                    startDateValue={
                                        field.value
                                            ? field.value.toISOString()
                                            : undefined
                                    }
                                    endDateValue={
                                        watch('endDate')
                                            ? watch('endDate')?.toISOString()
                                            : undefined
                                    }
                                />
                            )}
                        />
                    </div>

                    {/* Recipient Selection */}
                    <div className="flex flex-col gap-2">
                        <Text variant="bodySmall" fontWeight="font-semibold">
                            Select Recipient
                        </Text>
                        <Controller
                            name="recipient"
                            control={control}
                            render={({ field }) => (
                                <RadioButtonGroup
                                    id="selectRecipient"
                                    direction="column"
                                    radioButtonItems={recipientRadioItems}
                                    isRequired={true}
                                    errorMessage={errors.recipient?.message}
                                    size="medium"
                                    {...field}
                                    onChange={(value) => {
                                        // Extract the string value from the object
                                        const stringValue =
                                            typeof value === 'object' &&
                                            value?.value
                                                ? value.value
                                                : value;
                                        field.onChange(stringValue);
                                        trigger('recipient');
                                        // Clear email when switching to client
                                        if (stringValue === 'client') {
                                            setValue('email', '');
                                        }
                                    }}
                                />
                            )}
                        />
                        {errors.recipient && (
                            <Text variant="caption" textColor="text-red-500">
                                {errors.recipient.message}
                            </Text>
                        )}
                    </div>

                    {/* Email Input for Other Recipients */}
                    {recipient === 'other' && (
                        <div className="flex flex-col gap-2">
                            <Text
                                variant="bodySmall"
                                fontWeight="font-semibold"
                            >
                                Email Address
                            </Text>
                            <RenderFields
                                control={control}
                                errors={errors}
                                setValue={setValue}
                                watch={watch}
                                fields={[
                                    {
                                        id: 'email-input',
                                        type: 'text-input',
                                        name: 'email',
                                        placeholder: 'Enter email address',
                                        required: true,
                                        className: 'w-full',
                                    },
                                ]}
                            />
                        </div>
                    )}
                </form>
            </div>
        </Modal>
    );
};

export default ShareAnalyticsModal;
