import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { WinstonLogger } from '../../logger/winston-logger.service';
import * as AWS from 'aws-sdk';
import { PromiseResult } from 'aws-sdk/lib/request';
import { createTransport } from 'nodemailer';

@Injectable()
export class SESMailService {
	private readonly sesClient: AWS.SES;
	constructor(
		private configService: ConfigService,
		private loggerService: WinstonLogger
	) {
		this.sesClient = new AWS.SES({
			region: 'ap-south-1',
			accessKeyId: process.env.AWS_SES_ACCESS_KEY_ID,
			secretAccessKey: process.env.AWS_SES_SECRET_ACCESS_KEY
		});
	}

	async sendMail({
		body,
		subject,
		toMailAddress,
		ccMailAddress,
		pdfBuffers,
		pdfFileNames
	}: {
		toMailAddress: string;
		ccMailAddress?: string;
		subject: string;
		body: string;
		pdfBuffers?: Buffer[]; // Array of PDF buffers
		pdfFileNames?: string[]; // Array of file names for the PDFs
	}): Promise<PromiseResult<AWS.SES.SendEmailResponse, AWS.AWSError | null>> {
		return new Promise((resolve, rejects) => {
			try {
				const attachments = pdfBuffers?.map((buffer, idx) => ({
					filename: pdfFileNames?.[idx] || `Attachment_${idx}.pdf`,
					content: buffer
				}));

				const transporter = createTransport({
					SES: { ses: this.sesClient, aws: AWS }
				});
				// Use verified email for UAT environment
				const sourceEmail = process.env.NODE_ENV === 'uat'
					? '<EMAIL>'
					: process.env.SES_SOURCE_EMAIL;

				const mailOptions = {
					from: sourceEmail,
					subject,
					html: body,
					to: toMailAddress,
					cc: ccMailAddress,
					attachments
				};
				this.loggerService.log('mail options details', {
					from: sourceEmail,
					subject,
					html: body,
					to: toMailAddress,
					cc: ccMailAddress,
				});

				// Console log email details for development environment
				if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'dev') {
					console.log('\n=== 📧 EMAIL DEBUG (Development) ===');
					console.log('📧 FROM:', sourceEmail);
					console.log('📧 TO:', toMailAddress);
					console.log('📧 CC:', ccMailAddress || 'None');
					console.log('📧 SUBJECT:', subject);
					console.log('📧 CONTENT:');
					console.log(body);
					console.log('📎 ATTACHMENTS:', attachments?.map(a => a.filename).join(', ') || 'None');
					console.log('=== END EMAIL DEBUG ===\n');
				}
				transporter.sendMail(mailOptions, (err, response: any) => {
					if (err) {
						this.loggerService.error('Error sending email:', err.message);

						// Console log email details when sending fails in development
						if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'dev') {
							console.log('\n=== ❌ EMAIL FAILED (Development Debug) ===');
							console.log('📧 FROM:', sourceEmail);
							console.log('📧 TO:', toMailAddress);
							console.log('📧 CC:', ccMailAddress || 'None');
							console.log('📧 SUBJECT:', subject);
							console.log('📧 CONTENT:');
							console.log(body);
							console.log('📎 ATTACHMENTS:', attachments?.map(a => a.filename).join(', ') || 'None');
							console.log('❌ ERROR:', err.message);
							console.log('=== END EMAIL FAILED DEBUG ===\n');
						}
						//return rejects(err); // Log and reject the error.
					} else {
						// Console log successful email sending in development
						if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'dev') {
							console.log('\n=== ✅ EMAIL SENT SUCCESSFULLY (Development) ===');
							console.log('📧 FROM:', sourceEmail);
							console.log('📧 TO:', toMailAddress);
							console.log('📧 SUBJECT:', subject);
							console.log('✅ STATUS: Email sent successfully');
							console.log('=== END EMAIL SUCCESS DEBUG ===\n');
						}
					}
					this.loggerService.log('Email Response');
					resolve(response);
				});
			} catch (err) {
				const error = {
					err,
					message: 'something went wrong during sending mail '
				};
				this.loggerService.log('Email Error', {
					err
				});

				// Console log email details when there's a general error in development
				if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'dev') {
					console.log('\n=== ❌ EMAIL ERROR (Development Debug) ===');
					console.log('📧 TO:', toMailAddress);
					console.log('📧 SUBJECT:', subject);
					console.log('📧 CONTENT:');
					console.log(body);
					console.log('❌ ERROR:', err);
					console.log('=== END EMAIL ERROR DEBUG ===\n');
				}

				return rejects(error);
			}
		});
	}
}
