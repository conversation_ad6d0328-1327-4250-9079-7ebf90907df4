{"version": 3, "file": "database.config.js", "sourceRoot": "", "sources": ["../../../src/config/database.config.ts"], "names": [], "mappings": ";;AAAA,2CAA4C;AAC5C,mCAAgD;AAChD,yBAAyB;AAEzB,IAAA,eAAY,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;AAE/B,kBAAe,IAAA,mBAAU,EAAC,UAAU,EAAE,GAAG,EAAE;IACzC,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,4BAA4B;IACtG,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,6BAA6B,CAAC,CAAC,CAAC,oCAAoC,CAAA;IAExH,kEAAkE;IAClE,MAAM,SAAS,GAAG,UAAU;QAC1B,CAAC,CAAC;YACE,GAAG,EAAE;gBACH,kBAAkB,EAAE,CAAC,UAAU,EAAE,yDAAyD;gBAC1F,EAAE,EAAE,WAAW,IAAI,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC;oBAC3C,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,WAAW,CAAC;oBAC9B,CAAC,CAAC,SAAS;aACd;SACF;QACH,CAAC,CAAC,EAAE,CAAC;IAEP,OAAO;QACL,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,EAAE;QAC/B,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,EAAE,EAAE,EAAE,CAAC;QAC7C,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,EAAE;QACvC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,EAAE;QACvC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,EAAE;QAC5C,QAAQ,EAAE,CAAC,SAAS,GAAG,0BAA0B,CAAC;QAClD,UAAU,EAAE,CAAC,SAAS,GAAG,2BAA2B,CAAC;QACrD,WAAW,EAAE,KAAK;QAClB,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM;QACxC,mBAAmB,EAAE,oBAAoB;QACzC,KAAK,EAAE;YACL,GAAG,EAAE,EAAE;YACP,GAAG,EAAE,CAAC;YACN,iBAAiB,EAAE,KAAK;YACxB,uBAAuB,EAAE,IAAI;YAC7B,GAAG,SAAS,EAAE,yCAAyC;SACxD;KACF,CAAC;AACJ,CAAC,CAAC,CAAC"}