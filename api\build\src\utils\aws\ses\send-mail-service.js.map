{"version": 3, "file": "send-mail-service.js", "sourceRoot": "", "sources": ["../../../../../src/utils/aws/ses/send-mail-service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,2CAA+C;AAC/C,gFAAoE;AACpE,+BAA+B;AAE/B,2CAA6C;AAGtC,IAAM,cAAc,GAApB,MAAM,cAAc;IAE1B,YACS,aAA4B,EAC5B,aAA4B;QAD5B,kBAAa,GAAb,aAAa,CAAe;QAC5B,kBAAa,GAAb,aAAa,CAAe;QAEpC,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC;YAC5B,MAAM,EAAE,YAAY;YACpB,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB;YAC9C,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,yBAAyB;SACtD,CAAC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,EACd,IAAI,EACJ,OAAO,EACP,aAAa,EACb,aAAa,EACb,UAAU,EACV,YAAY,EAQZ;QACA,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;YACvC,IAAI,CAAC;gBACJ,MAAM,WAAW,GAAG,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,GAAG,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;oBACrD,QAAQ,EAAE,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAG,GAAG,CAAC,KAAI,cAAc,GAAG,MAAM;oBACxD,OAAO,EAAE,MAAM;iBACf,CAAC,CAAC,CAAC;gBAEJ,MAAM,WAAW,GAAG,IAAA,4BAAe,EAAC;oBACnC,GAAG,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE;iBACtC,CAAC,CAAC;gBACH,yCAAyC;gBACzC,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,KAAK;oBACjD,CAAC,CAAC,gBAAgB;oBAClB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC;gBAEhC,MAAM,WAAW,GAAG;oBACnB,IAAI,EAAE,WAAW;oBACjB,OAAO;oBACP,IAAI,EAAE,IAAI;oBACV,EAAE,EAAE,aAAa;oBACjB,EAAE,EAAE,aAAa;oBACjB,WAAW;iBACX,CAAC;gBACF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,sBAAsB,EAAE;oBAC9C,IAAI,EAAE,WAAW;oBACjB,OAAO;oBACP,IAAI,EAAE,IAAI;oBACV,EAAE,EAAE,aAAa;oBACjB,EAAE,EAAE,aAAa;iBACjB,CAAC,CAAC;gBAEH,wDAAwD;gBACxD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;oBAC9E,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;oBACtD,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;oBACrC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;oBACrC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,IAAI,MAAM,CAAC,CAAC;oBAC/C,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;oBACpC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;oBAC3B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBAClB,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,KAAI,MAAM,CAAC,CAAC;oBACvF,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;gBAC1C,CAAC;gBACD,WAAW,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,QAAa,EAAE,EAAE;oBACxD,IAAI,GAAG,EAAE,CAAC;wBACT,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,sBAAsB,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;wBAE9D,8DAA8D;wBAC9D,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;4BAC9E,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;4BAC5D,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;4BACrC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;4BACrC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,IAAI,MAAM,CAAC,CAAC;4BAC/C,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;4BACpC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;4BAC3B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;4BAClB,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,KAAI,MAAM,CAAC,CAAC;4BACvF,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;4BACrC,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;wBACjD,CAAC;wBACD,mDAAmD;oBACpD,CAAC;yBAAM,CAAC;wBACP,sDAAsD;wBACtD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;4BAC9E,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;4BACjE,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;4BACrC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;4BACrC,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;4BACpC,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;4BACjD,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;wBAClD,CAAC;oBACF,CAAC;oBACD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;oBACzC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACnB,CAAC,CAAC,CAAC;YACJ,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACd,MAAM,KAAK,GAAG;oBACb,GAAG;oBACH,OAAO,EAAE,2CAA2C;iBACpD,CAAC;gBACF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE;oBACrC,GAAG;iBACH,CAAC,CAAC;gBAEH,wEAAwE;gBACxE,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;oBAC9E,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;oBAC3D,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;oBACrC,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;oBACpC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;oBAC3B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBAClB,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;oBAC7B,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;gBAChD,CAAC;gBAED,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC;YACvB,CAAC;QACF,CAAC,CAAC,CAAC;IACJ,CAAC;CACD,CAAA;AA/HY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;qCAIY,sBAAa;QACb,sCAAa;GAJzB,cAAc,CA+H1B"}