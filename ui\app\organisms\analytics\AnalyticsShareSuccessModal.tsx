import React from 'react';
import { Modal } from '../../molecules';
import { Button, Text } from '../../atoms';
import Image from 'next/image';

interface AnalyticsShareSuccessModalProps {
    isOpen: boolean;
    onClose: () => void;
    title?: string;
}

const AnalyticsShareSuccessModal: React.FC<AnalyticsShareSuccessModalProps> = ({
    isOpen,
    onClose,
    title = "We're currently working on generating your document.",
}) => {
    return (
        <Modal
            isOpen={isOpen}
            isHeaderBorder={false}
            isModalSubTitle={true}
            modalSubTitle=""
            modalTitle=""
            onClose={onClose}
            dataAutomation="share-analytics-success"
        >
            <div className="gap-5 flex flex-col items-center">
                <div className="w-[111px] h-[111px] relative">
                    <Image
                        src={'/images/cartoon-rocket.png'}
                        layout="fill"
                        alt="success"
                    />
                </div>
                <div className="flex flex-col justify-center items-center gap-2">
                    <Text className="text-lg" fontWeight="font-semibold">
                        {title}
                    </Text>
                    <Text
                        variant="bodySmall"
                        textColor="text-neutral-600"
                        className="text-center"
                    >
                        It will be delivered to your inbox shortly.
                    </Text>
                </div>
            </div>
        </Modal>
    );
};

export default AnalyticsShareSuccessModal;
